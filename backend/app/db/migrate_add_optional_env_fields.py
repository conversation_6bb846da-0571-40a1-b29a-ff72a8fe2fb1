"""
Migration script to add optional environment variable columns to deployments table.

This script adds the following columns:
- traefik_subdomain
- middleware_manager_subdomain  
- nlweb_subdomain
- logs_subdomain

The script works with both PostgreSQL and SQLite databases.
"""

from sqlalchemy import create_engine, text, inspect
from app.core.config import get_settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_type(engine):
    """Determine if we're using PostgreSQL or SQLite"""
    return engine.dialect.name

def column_exists(engine, table_name, column_name):
    """Check if a column exists in a table (works for both PostgreSQL and SQLite)"""
    inspector = inspect(engine)
    columns = [col['name'] for col in inspector.get_columns(table_name)]
    return column_name in columns

def migrate_add_optional_env_fields():
    """Add optional environment variable columns to deployments table if they don't exist."""
    settings = get_settings()
    engine = create_engine(settings.DATABASE_URL)
    
    # Define the new columns to add
    new_columns = [
        'traefik_subdomain',
        'middleware_manager_subdomain', 
        'nlweb_subdomain',
        'logs_subdomain'
    ]
    
    db_type = get_database_type(engine)
    logger.info(f"Detected database type: {db_type}")
    
    try:
        with engine.connect() as conn:
            # Check if deployments table exists
            inspector = inspect(engine)
            if 'deployments' not in inspector.get_table_names():
                logger.error("Deployments table does not exist. Please run create_tables.py first.")
                return False
            
            logger.info("Checking for optional environment variable columns in deployments table...")
            
            for column_name in new_columns:
                if not column_exists(engine, 'deployments', column_name):
                    logger.info(f"Adding {column_name} column to deployments table...")
                    
                    # Use appropriate SQL syntax based on database type
                    if db_type == 'postgresql':
                        sql = f"ALTER TABLE deployments ADD COLUMN {column_name} VARCHAR NULL"
                    elif db_type == 'sqlite':
                        sql = f"ALTER TABLE deployments ADD COLUMN {column_name} TEXT NULL"
                    else:
                        logger.warning(f"Unsupported database type: {db_type}. Using generic VARCHAR.")
                        sql = f"ALTER TABLE deployments ADD COLUMN {column_name} VARCHAR NULL"
                    
                    conn.execute(text(sql))
                    conn.commit()
                    logger.info(f"Successfully added {column_name} column to deployments table")
                else:
                    logger.info(f"{column_name} column already exists in deployments table")
            
            logger.info("Migration completed successfully!")
            return True
                
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        raise

def verify_migration():
    """Verify that all optional environment variable columns have been added"""
    settings = get_settings()
    engine = create_engine(settings.DATABASE_URL)
    
    expected_columns = [
        'traefik_subdomain',
        'middleware_manager_subdomain', 
        'nlweb_subdomain',
        'logs_subdomain'
    ]
    
    logger.info("Verifying migration...")
    
    try:
        inspector = inspect(engine)
        existing_columns = [col['name'] for col in inspector.get_columns('deployments')]
        
        missing_columns = []
        for column in expected_columns:
            if column in existing_columns:
                logger.info(f"✅ {column} column exists")
            else:
                logger.error(f"❌ {column} column missing")
                missing_columns.append(column)
        
        if missing_columns:
            logger.error(f"Migration verification failed. Missing columns: {missing_columns}")
            return False
        else:
            logger.info("✅ Migration verification successful! All optional environment variable columns exist.")
            return True
            
    except Exception as e:
        logger.error(f"Error during verification: {e}")
        return False

def rollback_migration():
    """Rollback the migration by removing the added columns"""
    settings = get_settings()
    engine = create_engine(settings.DATABASE_URL)
    
    columns_to_remove = [
        'traefik_subdomain',
        'middleware_manager_subdomain', 
        'nlweb_subdomain',
        'logs_subdomain'
    ]
    
    db_type = get_database_type(engine)
    logger.info(f"Rolling back migration for database type: {db_type}")
    
    try:
        with engine.connect() as conn:
            for column_name in columns_to_remove:
                if column_exists(engine, 'deployments', column_name):
                    logger.info(f"Removing {column_name} column from deployments table...")
                    
                    if db_type == 'postgresql':
                        sql = f"ALTER TABLE deployments DROP COLUMN {column_name}"
                        conn.execute(text(sql))
                        conn.commit()
                        logger.info(f"Successfully removed {column_name} column")
                    elif db_type == 'sqlite':
                        logger.warning(f"SQLite does not support DROP COLUMN. Column {column_name} will remain but can be ignored.")
                    else:
                        logger.warning(f"Unsupported database type for rollback: {db_type}")
                else:
                    logger.info(f"{column_name} column does not exist, skipping removal")
            
            logger.info("Rollback completed!")
            
    except Exception as e:
        logger.error(f"Error during rollback: {e}")
        raise

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "migrate":
            migrate_add_optional_env_fields()
        elif command == "verify":
            verify_migration()
        elif command == "rollback":
            rollback_migration()
        else:
            print("Usage: python migrate_add_optional_env_fields.py [migrate|verify|rollback]")
            print("  migrate  - Add optional environment variable columns")
            print("  verify   - Verify that all columns have been added")
            print("  rollback - Remove the added columns (PostgreSQL only)")
    else:
        # Default action is to migrate
        migrate_add_optional_env_fields()
        verify_migration()
