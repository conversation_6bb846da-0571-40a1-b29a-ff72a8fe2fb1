import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts';
import { User, UserCreate } from '../api/types';
import { apiClient } from '../api';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import Modal from '../components/ui/modal';
import { Table } from '../components/ui/table';
import { Switch } from '../components/ui/switch';
import { toast } from '../components/ui/toast';

const UserManagement: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [userToDeactivate, setUserToDeactivate] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<UserCreate & { is_admin: boolean }>({
    username: '',
    email: '',
    password: '',
    is_admin: false
  });
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [userToAdjust, setUserToAdjust] = useState<User | null>(null);
  const [adjustAmount, setAdjustAmount] = useState<string>('10');
  const [adjustDescription, setAdjustDescription] = useState<string>('');

  // Search and pagination state
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showDeactivated, setShowDeactivated] = useState(false);

  // Redirect if not admin
  useEffect(() => {
    if (!isAuthenticated || !user?.is_admin) {
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated, user]);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.getUsers();
      setUsers(response);
    } catch (err) {
      setError('Failed to load users');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && user?.is_admin) {
      fetchUsers();
    }
  }, [isAuthenticated, user]);

  const handleCreateUser = async () => {
    try {
      setError(null);
      await apiClient.createUserAsAdmin({
        username: newUser.username,
        email: newUser.email,
        password: newUser.password,
        is_admin: newUser.is_admin
      });
      setShowCreateModal(false);
      setNewUser({ username: '', email: '', password: '', is_admin: false });
      await fetchUsers();
    } catch (err) {
      setError('Failed to create user');
      console.error('Error creating user:', err);
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setError(null);
      const isDeactivated = isUserDeactivated(userToDelete);

      if (isDeactivated) {
        // For deactivated users, admin can permanently delete them
        // This is a simple database deletion since they're already deactivated
        setError('Permanent deletion of deactivated users will be implemented separately for admin safety.');
      } else {
        setError('Cannot delete active users. Please use the deactivate function instead.');
      }

      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.detail || err?.message || 'Failed to delete user';
      setError(errorMessage);
      setShowDeleteModal(false);
      setUserToDelete(null);
      console.error('Error deleting user:', err);
    }
  };



  const handleDeactivateUser = async () => {
    if (!userToDeactivate) return;

    try {
      setError(null);

      // First check preconditions
      const preconditionCheck = await apiClient.checkDeactivationPreconditions(userToDeactivate.id);

      if (!preconditionCheck.can_deactivate) {
        // Show detailed error about what needs to be fixed
        const issues = [];
        if (preconditionCheck.issues.has_active_deployments) {
          issues.push(`${preconditionCheck.active_deployment_count} active deployment(s)`);
        }
        if (preconditionCheck.issues.has_positive_balance) {
          issues.push(`positive balance of €${preconditionCheck.user.balance.toFixed(2)}`);
        }

        setError(`Cannot deactivate user: ${issues.join(', ')}. Please resolve these issues manually first.`);
        setShowDeactivateModal(false);
        setUserToDeactivate(null);
        return;
      }

      // Preconditions met, proceed with deactivation
      const result = await apiClient.deactivateUser(userToDeactivate.id);
      setShowDeactivateModal(false);
      setUserToDeactivate(null);
      setError(`Success: ${result.message}`);
      await fetchUsers();
    } catch (err: any) {
      const errorMessage = err?.response?.data?.detail || err?.message || 'Failed to deactivate user';
      setError(errorMessage);
      setShowDeactivateModal(false);
      setUserToDeactivate(null);
      console.error('Error deactivating user:', err);
    }
  };

  const handleToggleAdmin = async (userId: number, currentStatus: boolean) => {
    try {
      setError(null);
      if (currentStatus) {
        await apiClient.removeAdminRole(userId);
      } else {
        await apiClient.makeUserAdmin(userId);
      }
      await fetchUsers();
    } catch (err) {
      setError('Failed to update admin status');
      console.error('Error updating admin status:', err);
    }
  };

  // Helper function to check if a user is deactivated
  const isUserDeactivated = (user: User) => {
    return user.username.startsWith('deactivated_');
  };

  // Filter users based on search query and deactivation status
  const filteredUsers = useMemo(() => {
    return users.filter(u => {
      // Filter by deactivation status
      const isDeactivated = isUserDeactivated(u);
      if (!showDeactivated && isDeactivated) return false;

      // Filter by search query
      if (!searchQuery) return true;

      const searchLower = searchQuery.toLowerCase();
      return (
        u.username.toLowerCase().includes(searchLower) ||
        u.email.toLowerCase().includes(searchLower) ||
        u.id.toString().includes(searchLower)
      );
    });
  }, [users, searchQuery, showDeactivated]);

  // Pagination logic
  const { totalPages, paginatedUsers } = useMemo(() => {
    const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    return { totalPages, paginatedUsers };
  }, [filteredUsers, currentPage, itemsPerPage]);

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, showDeactivated]);

  if (!isAuthenticated || !user?.is_admin) {
    return null; // Will redirect
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setShowCreateModal(true)}>
          Create New User
        </Button>
      </div>

      {/* Search Input and Filters */}
      <div className="flex gap-4 items-center flex-wrap">
        <Input
          type="text"
          placeholder="Search users by username, email, or ID..."
          className="max-w-md"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />

        <div className="flex items-center gap-2">
          <Switch
            checked={showDeactivated}
            onCheckedChange={setShowDeactivated}
            id="show-deactivated"
          />
          <Label htmlFor="show-deactivated" className="text-sm">
            Show deactivated users
          </Label>
        </div>

        <div className="text-sm text-muted-foreground">
          {filteredUsers.length} of {users.length} users
          {!showDeactivated && (
            <span className="ml-1 text-xs">
              ({users.filter(isUserDeactivated).length} deactivated hidden)
            </span>
          )}
        </div>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <p className="text-red-700">{error}</p>
        </Card>
      )}

      {loading ? (
        <Card className="p-8">
          <p className="text-center">Loading users...</p>
        </Card>
      ) : (
        <Card className="p-6">
          <Table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Admin</th>
                <th>Balance</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedUsers.map((u) => {
                const isDeactivated = isUserDeactivated(u);
                return (
                  <tr key={u.id} className={isDeactivated ? 'opacity-60 bg-muted/30' : ''}>
                    <td>{u.id}</td>
                    <td>
                      <div className="flex items-center gap-2">
                        {u.username}
                        {isDeactivated && (
                          <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                            Deactivated
                          </span>
                        )}
                      </div>
                    </td>
                    <td>{u.email}</td>
                  <td>
                    <Switch
                      checked={u.is_admin}
                      onCheckedChange={() => handleToggleAdmin(u.id, u.is_admin)}
                      disabled={u.id === user.id}
                    />
                  </td>
                  <td>{(u as any).balance != null ? parseFloat((u as any).balance).toFixed(2) : '—'}</td>
                  <td className="space-x-1">
                    <div className="flex flex-wrap gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Navigate to dashboard page filtered by this user
                          navigate(`/dashboard?user=${u.username}`);
                        }}
                      >
                        View Deployments
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Navigate to billing page filtered by this user
                          navigate(`/billing?user=${u.username}`);
                        }}
                      >
                        Show Billing
                      </Button>

                      {!isDeactivated && (
                        <>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => {
                              setUserToAdjust(u);
                              setAdjustAmount('10');
                              setAdjustDescription('');
                              setShowAdjustModal(true);
                            }}
                          >
                            Adjust Balance
                          </Button>

                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => {
                              setUserToDeactivate(u);
                              setShowDeactivateModal(true);
                            }}
                            disabled={u.id === user.id}
                          >
                            Deactivate
                          </Button>
                        </>
                      )}

                      {/* Delete button only for deactivated users */}
                      {isDeactivated && (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => {
                            setUserToDelete(u);
                            setShowDeleteModal(true);
                          }}
                          disabled={u.id === user.id}
                        >
                          Delete
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
                );
              })}
            </tbody>
          </Table>

          {/* Pagination */}
          {filteredUsers.length > 0 && (
            <div className="flex items-center justify-between text-sm text-muted-foreground mt-4 pt-4 border-t">
              <div className="flex items-center gap-2">
                <select
                  className="h-8 rounded-md border bg-background px-3 py-1 text-sm"
                  value={itemsPerPage}
                  onChange={(e) => setItemsPerPage(Number(e.target.value))}
                >
                  <option value={10}>10 per page</option>
                  <option value={20}>20 per page</option>
                  <option value={50}>50 per page</option>
                  <option value={100}>100 per page</option>
                </select>
                <span>
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredUsers.length)} of {filteredUsers.length} users
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    if (pageNum > totalPages) return null;

                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Create User Modal */}
      <Modal
        open={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create New User"
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              value={newUser.username}
              onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
              placeholder="Enter username"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              placeholder="Enter email"
            />
          </div>
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={newUser.password}
              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              placeholder="Enter password"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="is_admin"
              checked={newUser.is_admin}
              onCheckedChange={(checked) => setNewUser({ ...newUser, is_admin: checked })}
            />
            <Label htmlFor="is_admin">Administrator</Label>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={!newUser.username || !newUser.email || !newUser.password}
            >
              Create User
            </Button>
          </div>
        </div>
      </Modal>

      {/* Adjust Balance Modal */}
      <Modal
        open={showAdjustModal}
        onClose={() => setShowAdjustModal(false)}
        title={`Adjust Balance for ${userToAdjust?.username || ''}`}
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="amount">Amount (EUR)</Label>
            <Input
              id="amount"
              type="number"
              value={adjustAmount}
              onChange={(e) => setAdjustAmount(e.target.value)}
              placeholder="e.g. 10"
            />
            <p className="text-xs text-muted-foreground mt-1">Use negative numbers to subtract funds.</p>
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={adjustDescription}
              onChange={(e) => setAdjustDescription(e.target.value)}
              placeholder="e.g. courtesy credit"
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowAdjustModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (!userToAdjust) return;
                try {
                  await apiClient.adjustUserBalance(
                    userToAdjust.id,
                    parseFloat(adjustAmount || '0'),
                    adjustDescription || undefined
                  );
                  toast('Balance adjusted successfully', 'success');
                  setShowAdjustModal(false);
                  setUserToAdjust(null);
                  await fetchUsers();
                } catch (err) {
                  console.error('Failed to adjust balance', err);
                  toast('Failed to adjust balance', 'error');
                }
              }}
              disabled={!adjustAmount || isNaN(parseFloat(adjustAmount))}
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>



      {/* Deactivate User Modal */}
      <Modal
        open={showDeactivateModal}
        onClose={() => setShowDeactivateModal(false)}
        title="Deactivate User Account"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            <strong>Precondition Check:</strong> Before deactivating user "{userToDeactivate?.username}", please ensure:
          </p>
          <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
            <li>All active deployments have been manually deactivated</li>
            <li>User balance has been manually set to zero or negative</li>
          </ul>
          <p className="text-sm text-blue-600 font-medium">
            If preconditions are met, the user will be deactivated and their transaction history will be preserved for audit purposes.
          </p>
          <p className="text-sm text-orange-600 font-medium">
            If preconditions are NOT met, you will receive an error message with specific issues to resolve manually.
          </p>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDeactivateModal(false)}>
              Cancel
            </Button>
            <Button
              variant="secondary"
              onClick={handleDeactivateUser}
            >
              Deactivate Account
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete User Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete User"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to permanently delete user "{userToDelete?.username}"?
          </p>

          {userToDelete && isUserDeactivated(userToDelete) ? (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700 font-medium">
                ⚠️ This will permanently delete ALL user data including:
              </p>
              <ul className="text-sm text-red-600 mt-1 ml-4 list-disc">
                <li>All transactions and billing history</li>
                <li>All deployments (active ones will be destroyed)</li>
                <li>All referral and signup credits</li>
                <li>The user account permanently</li>
              </ul>
              <p className="text-sm text-red-700 font-medium mt-2">
                This action cannot be undone!
              </p>
            </div>
          ) : (
            <p className="text-sm text-red-600 font-medium">
              Warning: Only deactivated users can be deleted. Please deactivate the user first.
            </p>
          )}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
            >
              Delete User
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement;