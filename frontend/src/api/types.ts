// TypeScript types generated from OpenAPI schema

export type ServerType = 'new' | 'vps';

export interface User {
  id: number;
  username: string;
  email: string;
  is_admin: boolean;
  balance?: number;
  is_verified?: boolean;
  verified_at?: string | null;
}

export interface UserCreate {
  username: string;
  email: string;
  password: string;
  referral_code?: string;
}

export interface Deployment {
  id: number;
  user_id: number;
  package: 'Pangolin' | 'Pangolin+' | 'Pangolin+AI' | 'Coolify' | 'Coolify+';
  server_type?: ServerType;
  cloud_provider?: string | null;
  region?: string | null;
  instance_type?: string | null;
  vps_ip_address?: string | null;
  support_level: string;
  cost: number;
  deleted_at?: string | null;
  status: string;
  created_at: string;

  // User relationship for admin filtering
  user?: {
    id: number;
    username: string;
    email: string;
  } | null;

  // Optional fields
  komodo_provider_endpoint?: string | null;
  komodo_api_key?: string | null;
  komodo_api_secret?: string | null;
  github_token?: string | null;
  client_id?: string | null;
  client_name?: string | null;
  domain?: string | null;
  admin_email?: string | null;
  admin_username?: string | null;
  admin_password?: string | null;
  admin_subdomain?: string | null;
  postgres_user?: string | null;
  postgres_password?: string | null;
  postgres_host?: string | null;
  github_repo?: string | null;
  crowdsec_enrollment_key?: string | null;
  static_page_domain?: string | null;
  static_page_subdomain?: string | null;
  oauth_client_id?: string | null;
  oauth_client_secret?: string | null;
  openai_api_key?: string | null;
  komodo_host_ip?: string | null;
  komodo_passkey?: string | null;
  maxmind_license_key?: string | null;
  firewall_name?: string | null;
  instance_tags?: string[] | null;
  instance_ip?: string | null;
  user_ssh_key?: string | null;

  // Optional environment variables
  traefik_subdomain?: string | null;
  middleware_manager_subdomain?: string | null;
  nlweb_subdomain?: string | null;
  logs_subdomain?: string | null;
}

export interface DeploymentCreate {
  package: 'Pangolin' | 'Pangolin+' | 'Pangolin+AI' | 'Coolify' | 'Coolify+';
  server_type?: ServerType;
  // Cloud fields only for 'new'
  cloud_provider?: string | null;
  region?: string | null;
  instance_type?: string | null;
  // BYOVPS / existing
  vps_ip_address?: string | null;
  support_level: string;

  // Optional fields
  komodo_provider_endpoint?: string | null;
  komodo_api_key?: string | null;
  komodo_api_secret?: string | null;
  github_token?: string | null;
  client_id?: string | null;
  client_name?: string | null;
  domain?: string | null;
  admin_email?: string | null;
  admin_username?: string | null;
  admin_password?: string | null;
  admin_subdomain?: string | null;
  postgres_user?: string | null;
  postgres_password?: string | null;
  postgres_host?: string | null;
  github_repo?: string | null;
  crowdsec_enrollment_key?: string | null;
  static_page_domain?: string | null;
  static_page_subdomain?: string | null;
  oauth_client_id?: string | null;
  oauth_client_secret?: string | null;
  openai_api_key?: string | null;
  komodo_host_ip?: string | null;
  komodo_passkey?: string | null;
  maxmind_license_key?: string | null;
  firewall_name?: string | null;
  instance_tags?: string[] | null;
  instance_ip?: string | null;
  user_ssh_key?: string | null;

  // Optional environment variables
  traefik_subdomain?: string | null;
  middleware_manager_subdomain?: string | null;
  nlweb_subdomain?: string | null;
  logs_subdomain?: string | null;
}

export interface DeploymentDeleteResponse {
  message?: string;
  deploymentId?: number;
}

export interface DeploymentUpdate {
  package?: string | null;
  server_type?: ServerType | null;
  cloud_provider?: string | null;
  region?: string | null;
  instance_type?: string | null;
  vps_ip_address?: string | null;
  support_level?: string | null;
  komodo_provider_endpoint?: string | null;
  komodo_api_key?: string | null;
  komodo_api_secret?: string | null;
  github_token?: string | null;
  client_id?: string | null;
  client_name?: string | null;
  domain?: string | null;
  admin_email?: string | null;
  admin_username?: string | null;
  admin_password?: string | null;
  admin_subdomain?: string | null;
  postgres_user?: string | null;
  postgres_password?: string | null;
  postgres_host?: string | null;
  github_repo?: string | null;
  crowdsec_enrollment_key?: string | null;
  static_page_domain?: string | null;
  static_page_subdomain?: string | null;
  oauth_client_id?: string | null;
  oauth_client_secret?: string | null;
  openai_api_key?: string | null;
  komodo_host_ip?: string | null;
  komodo_passkey?: string | null;
  maxmind_license_key?: string | null;
  firewall_name?: string | null;
  instance_tags?: string[] | null;
  instance_ip?: string | null;
  user_ssh_key?: string | null;
}

export type CloudProviderName = 'AWS' | 'Google Cloud' | 'Azure' | 'Hetzner';

export interface CloudProvider {
  name: CloudProviderName;
  display_name: string;
  description?: string;
}

export interface Region {
  name: string;
  display_name: string;
}

export interface InstanceType {
  name: string;
  display_name: string;
  cpu: number;
  memory: number;
  hourly_cost: number;
}

export interface Package {
  name: 'Pangolin' | 'Pangolin+' | 'Pangolin+AI' | 'Coolify' | 'Coolify+';
  display_name: string;
  description: string;
  features: string[];
}

export interface PackageConfig {
  package: string;
  package_description: string;
  components: string[];
  details: Array<{
    name: string;
    required_env: Array<string | { name: string; hint?: string }>;
    optional_env?: Array<string | { name: string; hint?: string }>;
    description: string;
  }>;
}

export interface PricingCalculation {
  hourly_cost: number;
  daily_cost: number;
  monthly_cost: number;
  breakdown: {
    instance_cost: number;
    storage_cost: number;
    network_cost: number;
    support_cost: number;
    byovps_management_fee?: number;
  };
}

export interface APIError {
  detail: string;
  status_code: number;
}

export interface ServerInfo {
  id: number;
  name?: string | null;
  ip_address?: string | null;
  provider?: string | null;
  region?: string | null;
  instance_type?: string | null;
  status: string;
}
